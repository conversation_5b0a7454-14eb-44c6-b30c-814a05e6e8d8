-- PostgreSQL数据库和用户创建脚本
-- 请以PostgreSQL超级用户身份执行此脚本

-- 创建用户
CREATE USER makrite_user WITH PASSWORD 'makrite_2024_secure';

-- 创建数据库
CREATE DATABASE makrite_managementsystem 
    WITH OWNER = makrite_user
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TEMPLATE = template0;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE makrite_managementsystem TO makrite_user;

-- 连接到新数据库并授予schema权限
\c makrite_managementsystem
GRANT ALL ON SCHEMA public TO makrite_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO makrite_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO makrite_user;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO makrite_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO makrite_user;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

\echo '数据库创建完成！'
\echo '数据库名: makrite_managementsystem'
\echo '用户名: makrite_user'
\echo '请妥善保管数据库密码'
