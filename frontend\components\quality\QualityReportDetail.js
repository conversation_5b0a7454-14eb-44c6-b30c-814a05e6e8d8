/**
 * 质量检测报告详情组件
 * 显示检测报告的详细信息和文件列表
 */

export default {
    name: 'QualityReportDetail',
    props: {
        report: {
            type: Object,
            required: true
        },
        currentUser: {
            type: Object,
            required: true
        }
    },
    emits: ['close', 'edit', 'delete'],
    setup(props, { emit }) {
        const { computed } = Vue;

        // 权限检查
        const canDownload = computed(() => {
            return props.currentUser && (
                props.currentUser.role === 'admin' ||
                (props.currentUser.permissions && props.currentUser.permissions.includes('quality_download'))
            );
        });

        const canEdit = computed(() => {
            return props.currentUser && (
                props.currentUser.role === 'admin' ||
                props.report.uploaded_by === props.currentUser.id ||
                (props.currentUser.permissions && props.currentUser.permissions.includes('quality_manage'))
            );
        });

        const canDelete = computed(() => {
            return props.currentUser && (
                props.currentUser.role === 'admin' ||
                (props.currentUser.permissions && props.currentUser.permissions.includes('quality_manage'))
            );
        });rrentUser.role === 'ceo' ||
                (props.currentUser.permissions && props.currentUser.permissions.includes('quality_download'))
            );
        });

        const canEdit = computed(() => {
            if (!props.currentUser) return false;
            
            // 管理员可以编辑所有报告
            const isAdmin = props.currentUser.role === 'admin' ||
                           props.currentUser.role === 'CEO' ||
                           props.currentUser.role === '管理员' ||
                           props.currentUser.role === 'ceo' ||
                           (props.currentUser.permissions && props.currentUser.permissions.includes('quality_manage'));
            
            if (isAdmin) return true;
            
            // 报告创建者可以编辑自己的报告
            return props.report.uploaded_by === props.currentUser.id;
        });

        const canDelete = computed(() => {
            return canEdit.value; // 删除权限与编辑权限相同
        });

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleDateString('zh-CN');
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleString('zh-CN');
        }

        // 获取状态样式
        function getStatusClass(status) {
            switch (status) {
                case 'published':
                    return 'bg-green-100 text-green-800';
                case 'draft':
                    return 'bg-yellow-100 text-yellow-800';
                default:
                    return 'bg-gray-100 text-gray-800';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 'published':
                    return '已发布';
                case 'draft':
                    return '草稿';
                default:
                    return status;
            }
        }

        // 获取文件图标
        function getFileIcon(fileName) {
            const extension = fileName.split('.').pop().toLowerCase();
            switch (extension) {
                case 'pdf':
                    return '📄';
                case 'doc':
                case 'docx':
                    return '📝';
                case 'xls':
                case 'xlsx':
                    return '📊';
                default:
                    return '📎';
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 下载文件
        async function downloadFile(file) {
            if (!canDownload.value) {
                alert('您没有下载文件的权限');
                return;
            }

            try {
                const response = await axios.get(`/quality/files/${file.id}/download`, {
                    responseType: 'blob'
                });

                // 创建下载链接
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', file.original_filename);
                document.body.appendChild(link);
                link.click();
                link.remove();
                window.URL.revokeObjectURL(url);

                if (window.showNotification) {
                    window.showNotification('文件下载成功', 'success');
                }
            } catch (error) {
                console.error('下载文件失败:', error);
                if (window.showNotification) {
                    window.showNotification('下载文件失败: ' + error.message, 'error');
                } else {
                    alert('下载文件失败: ' + error.message);
                }
            }
        }

        // 关闭详情
        function handleClose() {
            emit('close');
        }

        // 编辑报告
        function handleEdit() {
            emit('edit', props.report);
        }

        // 删除报告
        function handleDelete() {
            if (confirm(`确定要删除检测报告"${props.report.title}"吗？此操作不可恢复。`)) {
                emit('delete', props.report);
            }
        }

        return {
            canDownload,
            canEdit,
            canDelete,
            formatDate,
            formatDateTime,
            getStatusClass,
            getStatusText,
            getFileIcon,
            formatFileSize,
            downloadFile,
            handleClose,
            handleEdit,
            handleDelete
        };
    },
    template: `
        <div class="quality-report-detail bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
            <!-- 头部 -->
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h2 class="text-2xl font-bold text-gray-800">检测报告详情</h2>
                <button
                    @click="handleClose"
                    class="text-gray-400 hover:text-gray-600 transition-colors"
                >
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- 内容 -->
            <div class="p-6 space-y-6">
                <!-- 基本信息 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">报告编号</label>
                        <p class="text-lg font-mono text-gray-900">{{ report.report_number }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full" :class="getStatusClass(report.status)">
                            {{ getStatusText(report.status) }}
                        </span>
                    </div>
                </div>

                <!-- 报告标题 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">报告标题</label>
                    <h3 class="text-xl font-semibold text-gray-900">{{ report.title }}</h3>
                </div>

                <!-- 检测信息 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">检测类型</label>
                        <p class="text-gray-900">{{ report.test_type }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">检测日期</label>
                        <p class="text-gray-900">{{ formatDate(report.test_date) }}</p>
                    </div>
                </div>

                <!-- 报告描述 -->
                <div v-if="report.description">
                    <label class="block text-sm font-medium text-gray-700 mb-2">报告描述</label>
                    <div class="bg-gray-50 rounded-md p-4">
                        <p class="text-gray-900 whitespace-pre-wrap">{{ report.description }}</p>
                    </div>
                </div>

                <!-- 详细信息 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 样品信息 -->
                    <div v-if="report.sample_info">
                        <label class="block text-sm font-medium text-gray-700 mb-2">样品信息</label>
                        <div class="bg-gray-50 rounded-md p-3">
                            <p class="text-gray-900 text-sm whitespace-pre-wrap">{{ report.sample_info }}</p>
                        </div>
                    </div>

                    <!-- 检测方法 -->
                    <div v-if="report.test_method">
                        <label class="block text-sm font-medium text-gray-700 mb-2">检测方法</label>
                        <div class="bg-gray-50 rounded-md p-3">
                            <p class="text-gray-900 text-sm whitespace-pre-wrap">{{ report.test_method }}</p>
                        </div>
                    </div>

                    <!-- 检测标准 -->
                    <div v-if="report.test_standard">
                        <label class="block text-sm font-medium text-gray-700 mb-2">检测标准</label>
                        <div class="bg-gray-50 rounded-md p-3">
                            <p class="text-gray-900 text-sm whitespace-pre-wrap">{{ report.test_standard }}</p>
                        </div>
                    </div>

                    <!-- 检测结果 -->
                    <div v-if="report.test_result">
                        <label class="block text-sm font-medium text-gray-700 mb-2">检测结果</label>
                        <div class="bg-gray-50 rounded-md p-3">
                            <p class="text-gray-900 text-sm whitespace-pre-wrap">{{ report.test_result }}</p>
                        </div>
                    </div>
                </div>

                <!-- 结论 -->
                <div v-if="report.conclusion">
                    <label class="block text-sm font-medium text-gray-700 mb-2">结论</label>
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <p class="text-gray-900 whitespace-pre-wrap">{{ report.conclusion }}</p>
                    </div>
                </div>

                <!-- 附件文件 -->
                <div v-if="report.files && report.files.length > 0">
                    <label class="block text-sm font-medium text-gray-700 mb-3">附件文件</label>
                    <div class="space-y-2">
                        <div
                            v-for="file in report.files"
                            :key="file.id"
                            class="flex items-center justify-between p-4 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
                        >
                            <div class="flex items-center space-x-3">
                                <span class="text-2xl">{{ getFileIcon(file.original_filename) }}</span>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ file.original_filename }}</p>
                                    <p class="text-xs text-gray-500">
                                        {{ formatFileSize(file.file_size) }} • 
                                        上传于 {{ formatDateTime(file.uploaded_at) }}
                                    </p>
                                </div>
                            </div>
                            <button
                                v-if="canDownload"
                                @click="downloadFile(file)"
                                class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                            >
                                下载
                            </button>
                            <span v-else class="text-sm text-gray-400">无下载权限</span>
                        </div>
                    </div>
                </div>

                <!-- 上传信息 -->
                <div class="border-t border-gray-200 pt-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
                        <div>
                            <label class="block font-medium text-gray-700 mb-1">上传人员</label>
                            <p>{{ report.uploader_name }} ({{ report.uploader_department }})</p>
                        </div>
                        <div>
                            <label class="block font-medium text-gray-700 mb-1">上传时间</label>
                            <p>{{ formatDateTime(report.uploaded_at) }}</p>
                        </div>
                        <div>
                            <label class="block font-medium text-gray-700 mb-1">创建时间</label>
                            <p>{{ formatDateTime(report.created_at) }}</p>
                        </div>
                        <div v-if="report.updated_at !== report.created_at">
                            <label class="block font-medium text-gray-700 mb-1">最后更新</label>
                            <p>{{ formatDateTime(report.updated_at) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
                <button
                    @click="handleClose"
                    class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                    关闭
                </button>
                <button
                    v-if="canEdit"
                    @click="handleEdit"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                    编辑
                </button>
                <button
                    v-if="canDelete"
                    @click="handleDelete"
                    class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                    删除
                </button>
            </div>
        </div>
    `
};
