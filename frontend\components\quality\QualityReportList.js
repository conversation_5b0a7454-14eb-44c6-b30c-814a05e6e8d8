/**
 * 质量检测报告列表组件
 * 显示检测报告列表，支持筛选、分页和操作
 */

export default {
    name: 'QualityReportList',
    props: {
        currentUser: {
            type: Object,
            required: true
        }
    },
    emits: ['view-detail', 'edit-report', 'delete-report'],
    setup(props, { emit }) {
        const { ref, reactive, computed, onMounted } = Vue;

        // 数据状态
        const reports = ref([]);
        const loading = ref(false);
        const pagination = reactive({
            page: 1,
            limit: 10,
            total: 0,
            pages: 0
        });

        // 筛选条件
        const filters = reactive({
            search: '',
            testType: '',
            status: '',
            startDate: '',
            endDate: ''
        });

        // 检测类型选项
        const testTypes = [
            '原材料检测',
            '半成品检测',
            '成品检测',
            '环境检测',
            '设备检测',
            '工艺检测',
            '其他检测'
        ];

        // 状态选项
        const statusOptions = [
            { value: 'published', label: '已发布' },
            { value: 'draft', label: '草稿' }
        ];

        // 权限检查
        const canUpload = computed(() => {
            return props.currentUser && (
                props.currentUser.role === 'admin' ||
                (props.currentUser.permissions && props.currentUser.permissions.includes('quality_upload'))
            );
        });

        const canManage = computed(() => {
            return props.currentUser && (
                props.currentUser.role === 'admin' ||
                (props.currentUser.permissions && props.currentUser.permissions.includes('quality_manage'))
            );
        });

        const canDownload = computed(() => {
            return props.currentUser && (
                props.currentUser.role === 'admin' ||
                props.currentUser.role === 'CEO' ||
                props.currentUser.role === '管理员' ||
                props.currentUser.role === 'ceo' ||
                (props.currentUser.permissions && props.currentUser.permissions.includes('quality_download'))
            );
        });

        // 获取报告列表
        async function fetchReports() {
            try {
                loading.value = true;

                const params = {
                    page: pagination.page,
                    limit: pagination.limit,
                    ...filters
                };

                // 移除空值参数
                Object.keys(params).forEach(key => {
                    if (params[key] === '' || params[key] === null || params[key] === undefined) {
                        delete params[key];
                    }
                });

                // 添加时间戳参数强制绕过缓存
                const timestamp = Date.now();
                params._t = timestamp;
                params._r = Math.random();

                const response = await axios.get('/quality', {
                    params,
                    headers: {
                        'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json',
                        // 添加防缓存请求头
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });

                if (response.data.success) {
                    reports.value = response.data.data;
                    Object.assign(pagination, response.data.pagination);
                } else {
                    throw new Error(response.data.message);
                }
            } catch (error) {
                console.error('获取检测报告列表失败:', error);
                if (window.showNotification) {
                    window.showNotification('获取检测报告列表失败: ' + error.message, 'error');
                } else {
                    alert('获取检测报告列表失败: ' + error.message);
                }
            } finally {
                loading.value = false;
            }
        }

        // 搜索
        function handleSearch() {
            pagination.page = 1;
            fetchReports();
        }

        // 重置筛选
        function resetFilters() {
            Object.assign(filters, {
                search: '',
                testType: '',
                status: '',
                startDate: '',
                endDate: ''
            });
            pagination.page = 1;
            fetchReports();
        }

        // 分页处理
        function handlePageChange(page) {
            pagination.page = page;
            fetchReports();
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleDateString('zh-CN');
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleString('zh-CN');
        }

        // 获取状态样式
        function getStatusClass(status) {
            switch (status) {
                case 'published':
                    return 'bg-green-100 text-green-800';
                case 'draft':
                    return 'bg-yellow-100 text-yellow-800';
                default:
                    return 'bg-gray-100 text-gray-800';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            const option = statusOptions.find(opt => opt.value === status);
            return option ? option.label : status;
        }

        // 查看详情
        function viewDetail(report) {
            emit('view-detail', report);
        }

        // 编辑报告
        function editReport(report) {
            emit('edit-report', report);
        }

        // 删除报告
        async function deleteReport(report) {
            if (confirm(`确定要删除检测报告"${report.title}"吗？此操作不可恢复。`)) {
                // 找到要删除的报告索引
                const reportIndex = reports.value.findIndex(r => r.id === report.id);
                if (reportIndex === -1) return;

                const deletedReport = reports.value[reportIndex];

                // 乐观更新：立即从列表中移除
                reports.value.splice(reportIndex, 1);

                // 发送删除事件到父组件处理API调用
                emit('delete-report', { report, deletedReport, reportIndex });
            }
        }

        // 恢复删除的报告（供父组件调用）
        function restoreDeletedReport(deletedReport, reportIndex) {
            if (reportIndex >= 0 && reportIndex <= reports.value.length) {
                reports.value.splice(reportIndex, 0, deletedReport);
            }
        }

        // 检查是否可以编辑/删除
        function canEditOrDelete(report) {
            if (!props.currentUser) return false;
            
            // 管理员可以编辑/删除所有报告
            if (canManage.value) return true;
            
            // 报告创建者可以编辑/删除自己的报告
            return report.uploaded_by === props.currentUser.id;
        }

        // 生成分页按钮
        const paginationButtons = computed(() => {
            const buttons = [];
            const current = pagination.page;
            const total = pagination.pages;
            
            // 显示的页码范围
            let start = Math.max(1, current - 2);
            let end = Math.min(total, current + 2);
            
            // 调整范围以显示5个按钮
            if (end - start < 4) {
                if (start === 1) {
                    end = Math.min(total, start + 4);
                } else {
                    start = Math.max(1, end - 4);
                }
            }
            
            for (let i = start; i <= end; i++) {
                buttons.push(i);
            }
            
            return buttons;
        });

        // 初始化
        onMounted(() => {
            fetchReports();
        });

        return {
            reports,
            loading,
            pagination,
            filters,
            testTypes,
            statusOptions,
            canUpload,
            canManage,
            canDownload,
            fetchReports,
            handleSearch,
            resetFilters,
            handlePageChange,
            formatDate,
            formatDateTime,
            getStatusClass,
            getStatusText,
            viewDetail,
            editReport,
            deleteReport,
            restoreDeletedReport,
            canEditOrDelete,
            paginationButtons
        };
    },
    template: `
        <div class="quality-report-list">
            <!-- 筛选区域 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
                    <!-- 搜索 -->
                    <div>
                        <input
                            type="text"
                            v-model="filters.search"
                            placeholder="搜索报告标题、编号..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            @keyup.enter="handleSearch"
                        >
                    </div>

                    <!-- 检测类型 -->
                    <div>
                        <select
                            v-model="filters.testType"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">检测类型</option>
                            <option v-for="type in testTypes" :key="type" :value="type">{{ type }}</option>
                        </select>
                    </div>

                    <!-- 状态 -->
                    <div>
                        <select
                            v-model="filters.status"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">状态</option>
                            <option v-for="option in statusOptions" :key="option.value" :value="option.value">
                                {{ option.label }}
                            </option>
                        </select>
                    </div>

                    <!-- 开始日期 -->
                    <div>
                        <input
                            type="date"
                            v-model="filters.startDate"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>

                    <!-- 结束日期 -->
                    <div>
                        <input
                            type="date"
                            v-model="filters.endDate"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                </div>

                <div class="flex justify-between items-center">
                    <div class="flex space-x-2">
                        <button
                            @click="handleSearch"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                            搜索
                        </button>
                        <button
                            @click="resetFilters"
                            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                        >
                            重置
                        </button>
                    </div>

                    <div class="text-sm text-gray-600">
                        共 {{ pagination.total }} 条记录
                    </div>
                </div>
            </div>

            <!-- 报告列表 -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div v-if="loading" class="p-8 text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p class="mt-2 text-gray-600">加载中...</p>
                </div>

                <div v-else-if="reports.length === 0" class="p-8 text-center text-gray-500">
                    暂无检测报告数据
                </div>

                <div v-else class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    报告信息
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    检测类型
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    检测日期
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    上传人员
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    状态
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    文件数量
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="report in reports" :key="report.id" class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ report.title }}</div>
                                        <div class="text-sm text-gray-500">{{ report.report_number }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ report.test_type }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ formatDate(report.test_date) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm text-gray-900">{{ report.uploader_name }}</div>
                                        <div class="text-sm text-gray-500">{{ report.uploader_department }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" :class="getStatusClass(report.status)">
                                        {{ getStatusText(report.status) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ report.file_count || 0 }} 个文件
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                    <button
                                        @click="viewDetail(report)"
                                        class="text-blue-600 hover:text-blue-900 transition-colors"
                                    >
                                        查看
                                    </button>
                                    <button
                                        v-if="canEditOrDelete(report)"
                                        @click="editReport(report)"
                                        class="text-green-600 hover:text-green-900 transition-colors"
                                    >
                                        编辑
                                    </button>
                                    <button
                                        v-if="canEditOrDelete(report)"
                                        @click="deleteReport(report)"
                                        class="text-red-600 hover:text-red-900 transition-colors"
                                    >
                                        删除
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div v-if="pagination.pages > 1" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <button
                            @click="handlePageChange(pagination.page - 1)"
                            :disabled="pagination.page <= 1"
                            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            上一页
                        </button>
                        <button
                            @click="handlePageChange(pagination.page + 1)"
                            :disabled="pagination.page >= pagination.pages"
                            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            下一页
                        </button>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                显示第 <span class="font-medium">{{ (pagination.page - 1) * pagination.limit + 1 }}</span> 到 
                                <span class="font-medium">{{ Math.min(pagination.page * pagination.limit, pagination.total) }}</span> 条，
                                共 <span class="font-medium">{{ pagination.total }}</span> 条记录
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                <button
                                    @click="handlePageChange(pagination.page - 1)"
                                    :disabled="pagination.page <= 1"
                                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    上一页
                                </button>
                                <button
                                    v-for="page in paginationButtons"
                                    :key="page"
                                    @click="handlePageChange(page)"
                                    :class="[
                                        'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                                        page === pagination.page
                                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                    ]"
                                >
                                    {{ page }}
                                </button>
                                <button
                                    @click="handlePageChange(pagination.page + 1)"
                                    :disabled="pagination.page >= pagination.pages"
                                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    下一页
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
};
