{"development": {"host": "localhost", "port": 5432, "database": "makrite_managementsystem", "username": "makrite_user", "password": "makrite_2024_secure", "dialect": "postgres", "logging": false, "pool": {"max": 20, "min": 5, "acquire": 30000, "idle": 10000}}, "production": {"host": "localhost", "port": 5432, "database": "makrite_managementsystem", "username": "makrite_user", "password": "makrite_2024_secure", "dialect": "postgres", "logging": false, "pool": {"max": 30, "min": 10, "acquire": 30000, "idle": 10000}}}