/**
 * SQLite到PostgreSQL数据迁移脚本
 * 完整迁移所有表结构和数据
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const logger = require('../utils/logger');
const PostgreSQLManager = require('../database/postgresqlManager');

class SQLiteToPostgreSQLMigrator {
    constructor() {
        this.sqliteDbPath = path.join(__dirname, '../database/application_system.db');
        this.sqliteDb = null;
        this.postgresDb = null;
        this.migrationStats = {
            tablesCreated: 0,
            recordsMigrated: 0,
            indexesCreated: 0,
            errors: []
        };
    }

    /**
     * 初始化数据库连接
     */
    async initialize() {
        try {
            // 检查SQLite数据库是否存在
            if (!fs.existsSync(this.sqliteDbPath)) {
                throw new Error(`SQLite数据库文件不存在: ${this.sqliteDbPath}`);
            }

            // 连接SQLite数据库
            this.sqliteDb = new Database(this.sqliteDbPath, { readonly: true });
            logger.info('SQLite数据库连接成功');

            // 连接PostgreSQL数据库
            this.postgresDb = new PostgreSQLManager();
            await this.postgresDb.testConnection();
            logger.info('PostgreSQL数据库连接成功');

            return true;
        } catch (error) {
            logger.error('数据库连接初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取SQLite表结构信息
     */
    getSQLiteTableInfo(tableName) {
        try {
            const pragma = this.sqliteDb.prepare(`PRAGMA table_info(${tableName})`).all();
            const foreignKeys = this.sqliteDb.prepare(`PRAGMA foreign_key_list(${tableName})`).all();
            const indexes = this.sqliteDb.prepare(`PRAGMA index_list(${tableName})`).all();

            return {
                columns: pragma,
                foreignKeys: foreignKeys,
                indexes: indexes
            };
        } catch (error) {
            logger.error(`获取表 ${tableName} 结构信息失败:`, error);
            return null;
        }
    }

    /**
     * 将SQLite数据类型转换为PostgreSQL数据类型
     */
    convertDataType(sqliteType, columnName) {
        const type = sqliteType.toUpperCase();
        
        // 特殊字段处理
        if (columnName === 'id' && type === 'TEXT') {
            return 'UUID';
        }
        
        if (columnName === 'id' && type === 'INTEGER') {
            return 'SERIAL PRIMARY KEY';
        }

        // 通用类型转换
        const typeMap = {
            'TEXT': 'TEXT',
            'INTEGER': 'INTEGER',
            'REAL': 'REAL',
            'NUMERIC': 'NUMERIC',
            'BLOB': 'BYTEA',
            'BOOLEAN': 'BOOLEAN'
        };

        return typeMap[type] || 'TEXT';
    }

    /**
     * 生成PostgreSQL表创建SQL
     */
    generateCreateTableSQL(tableName, tableInfo) {
        const columns = tableInfo.columns.map(col => {
            let columnDef = `"${col.name}" ${this.convertDataType(col.type, col.name)}`;
            
            // 处理NOT NULL约束
            if (col.notnull === 1) {
                columnDef += ' NOT NULL';
            }
            
            // 处理默认值
            if (col.dflt_value !== null) {
                let defaultValue = col.dflt_value;
                
                // 特殊默认值处理
                if (defaultValue === 'CURRENT_TIMESTAMP') {
                    defaultValue = 'CURRENT_TIMESTAMP';
                } else if (defaultValue.startsWith("'") && defaultValue.endsWith("'")) {
                    // 保持字符串默认值的引号
                    defaultValue = defaultValue;
                } else if (defaultValue === '[]') {
                    defaultValue = "'[]'";
                } else if (!isNaN(defaultValue)) {
                    // 数字默认值不需要引号
                    defaultValue = defaultValue;
                }
                
                columnDef += ` DEFAULT ${defaultValue}`;
            }
            
            return columnDef;
        });

        // 添加主键约束（如果不是SERIAL类型）
        const primaryKeyCol = tableInfo.columns.find(col => col.pk === 1);
        if (primaryKeyCol && !this.convertDataType(primaryKeyCol.type, primaryKeyCol.name).includes('SERIAL')) {
            columns.push(`PRIMARY KEY ("${primaryKeyCol.name}")`);
        }

        // 添加外键约束
        tableInfo.foreignKeys.forEach(fk => {
            columns.push(`FOREIGN KEY ("${fk.from}") REFERENCES "${fk.table}" ("${fk.to}")`);
        });

        return `CREATE TABLE IF NOT EXISTS "${tableName}" (\n  ${columns.join(',\n  ')}\n);`;
    }

    /**
     * 创建PostgreSQL表结构
     */
    async createPostgreSQLTables() {
        logger.info('开始创建PostgreSQL表结构...');
        
        try {
            // 获取所有表名
            const tables = this.sqliteDb.prepare(`
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            `).all();

            logger.info(`发现 ${tables.length} 个表需要迁移`);

            // 按依赖顺序创建表（先创建被引用的表）
            const tableOrder = [
                'users', 'permission_templates', 'factories', 'equipment', 
                'products', 'applications', 'approval_history', 'application_attachments',
                'schedules', 'schedule_progress', 'resources', 'operators',
                'operator_skills', 'equipment_operators', 'equipment_capabilities',
                'equipment_maintenance', 'equipment_health', 'equipment_health_history',
                'production_processes', 'quality_reports', 'quality_report_files',
                'quality_report_numbers', 'migrations'
            ];

            // 创建表
            for (const tableName of tableOrder) {
                const tableExists = tables.find(t => t.name === tableName);
                if (!tableExists) continue;

                logger.info(`创建表: ${tableName}`);
                
                const tableInfo = this.getSQLiteTableInfo(tableName);
                if (!tableInfo) {
                    this.migrationStats.errors.push(`无法获取表 ${tableName} 的结构信息`);
                    continue;
                }

                const createSQL = this.generateCreateTableSQL(tableName, tableInfo);
                
                try {
                    await this.postgresDb.query(createSQL);
                    this.migrationStats.tablesCreated++;
                    logger.info(`表 ${tableName} 创建成功`);
                } catch (error) {
                    logger.error(`创建表 ${tableName} 失败:`, error);
                    this.migrationStats.errors.push(`创建表 ${tableName} 失败: ${error.message}`);
                }
            }

            // 创建剩余的表（如果有）
            for (const table of tables) {
                if (!tableOrder.includes(table.name)) {
                    logger.info(`创建剩余表: ${table.name}`);
                    
                    const tableInfo = this.getSQLiteTableInfo(table.name);
                    if (tableInfo) {
                        const createSQL = this.generateCreateTableSQL(table.name, tableInfo);
                        try {
                            await this.postgresDb.query(createSQL);
                            this.migrationStats.tablesCreated++;
                        } catch (error) {
                            logger.error(`创建表 ${table.name} 失败:`, error);
                            this.migrationStats.errors.push(`创建表 ${table.name} 失败: ${error.message}`);
                        }
                    }
                }
            }

            logger.info(`表结构创建完成，共创建 ${this.migrationStats.tablesCreated} 个表`);
            
        } catch (error) {
            logger.error('创建PostgreSQL表结构失败:', error);
            throw error;
        }
    }

    /**
     * 迁移表数据
     */
    async migrateTableData(tableName) {
        try {
            logger.info(`开始迁移表 ${tableName} 的数据...`);
            
            // 获取SQLite表数据
            const data = this.sqliteDb.prepare(`SELECT * FROM "${tableName}"`).all();
            
            if (data.length === 0) {
                logger.info(`表 ${tableName} 无数据，跳过`);
                return 0;
            }

            // 获取列名
            const columns = Object.keys(data[0]);
            
            // 批量插入数据
            const batchSize = 1000;
            let totalInserted = 0;
            
            for (let i = 0; i < data.length; i += batchSize) {
                const batch = data.slice(i, i + batchSize);
                const values = batch.map(row => columns.map(col => row[col]));
                
                try {
                    const result = await this.postgresDb.batchInsert(tableName, columns, values);
                    totalInserted += result.rowCount;
                } catch (error) {
                    logger.error(`批量插入表 ${tableName} 数据失败:`, error);
                    // 尝试逐行插入
                    for (const row of batch) {
                        try {
                            const placeholders = columns.map((_, index) => `$${index + 1}`).join(', ');
                            const query = `INSERT INTO "${tableName}" (${columns.map(c => `"${c}"`).join(', ')}) VALUES (${placeholders})`;
                            const values = columns.map(col => row[col]);
                            await this.postgresDb.query(query, values);
                            totalInserted++;
                        } catch (rowError) {
                            logger.error(`插入行数据失败:`, { table: tableName, row, error: rowError.message });
                            this.migrationStats.errors.push(`表 ${tableName} 行数据插入失败: ${rowError.message}`);
                        }
                    }
                }
            }

            logger.info(`表 ${tableName} 数据迁移完成，共迁移 ${totalInserted} 条记录`);
            return totalInserted;
            
        } catch (error) {
            logger.error(`迁移表 ${tableName} 数据失败:`, error);
            this.migrationStats.errors.push(`迁移表 ${tableName} 数据失败: ${error.message}`);
            return 0;
        }
    }

    /**
     * 迁移所有表数据
     */
    async migrateAllData() {
        logger.info('开始迁移所有表数据...');
        
        try {
            const tables = this.sqliteDb.prepare(`
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            `).all();

            for (const table of tables) {
                const recordCount = await this.migrateTableData(table.name);
                this.migrationStats.recordsMigrated += recordCount;
            }

            logger.info(`数据迁移完成，共迁移 ${this.migrationStats.recordsMigrated} 条记录`);
            
        } catch (error) {
            logger.error('数据迁移失败:', error);
            throw error;
        }
    }

    /**
     * 执行完整迁移
     */
    async migrate() {
        try {
            logger.info('开始SQLite到PostgreSQL数据迁移...');
            
            // 1. 初始化连接
            await this.initialize();
            
            // 2. 创建表结构
            await this.createPostgreSQLTables();
            
            // 3. 迁移数据
            await this.migrateAllData();
            
            // 4. 显示迁移统计
            this.showMigrationStats();
            
            logger.info('数据迁移完成！');
            return true;
            
        } catch (error) {
            logger.error('数据迁移失败:', error);
            this.showMigrationStats();
            return false;
        } finally {
            // 清理连接
            if (this.sqliteDb) {
                this.sqliteDb.close();
            }
            if (this.postgresDb) {
                await this.postgresDb.close();
            }
        }
    }

    /**
     * 显示迁移统计信息
     */
    showMigrationStats() {
        console.log('\n' + '='.repeat(60));
        console.log('数据迁移统计');
        console.log('='.repeat(60));
        console.log(`创建表数量: ${this.migrationStats.tablesCreated}`);
        console.log(`迁移记录数: ${this.migrationStats.recordsMigrated}`);
        console.log(`错误数量: ${this.migrationStats.errors.length}`);
        
        if (this.migrationStats.errors.length > 0) {
            console.log('\n错误详情:');
            this.migrationStats.errors.forEach((error, index) => {
                console.log(`${index + 1}. ${error}`);
            });
        }
        console.log('='.repeat(60));
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const migrator = new SQLiteToPostgreSQLMigrator();
    migrator.migrate().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = SQLiteToPostgreSQLMigrator;
