/**
 * 动态配置管理器
 * 从后端获取配置信息，避免硬编码
 */

class ConfigManager {
    constructor() {
        this.config = null;
        this.isLoaded = false;
        this.loadPromise = null;
    }

    /**
     * 获取配置（异步）
     * @returns {Promise<Object>} 配置对象
     */
    async getConfig() {
        if (this.isLoaded && this.config) {
            return this.config;
        }

        // 如果正在加载，返回现有的Promise
        if (this.loadPromise) {
            return this.loadPromise;
        }

        // 开始加载配置
        this.loadPromise = this.loadConfig();
        return this.loadPromise;
    }

    /**
     * 从后端加载配置
     * @returns {Promise<Object>} 配置对象
     */
    async loadConfig() {
        try {
            // 动态获取当前主机和端口
            const currentUrl = window.location;
            const baseUrl = `${currentUrl.protocol}//${currentUrl.hostname}`;
            
            // 尝试常用端口获取配置
            const ports = [currentUrl.port || (currentUrl.protocol === 'https:' ? '443' : '80'), '3000', '5050'];
            
            for (const port of ports) {
                try {
                    const configUrl = `${baseUrl}:${port}/api/config`;
                    console.log(`尝试从 ${configUrl} 获取配置...`);
                    
                    const response = await fetch(configUrl, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        timeout: 5000
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.success) {
                            this.config = result.data;
                            this.isLoaded = true;
                            console.log('✅ 配置加载成功:', this.config);
                            return this.config;
                        }
                    }
                } catch (error) {
                    console.warn(`端口 ${port} 配置获取失败:`, error.message);
                    continue;
                }
            }

            // 如果所有端口都失败，使用默认配置
            throw new Error('无法从任何端口获取配置');

        } catch (error) {
            console.error('配置加载失败，使用默认配置:', error);
            
            // 使用默认配置作为后备
            this.config = this.getDefaultConfig();
            this.isLoaded = true;
            return this.config;
        }
    }

    /**
     * 获取默认配置（后备方案）
     * @returns {Object} 默认配置
     */
    getDefaultConfig() {
        const currentUrl = window.location;
        return {
            apiUrl: `${currentUrl.protocol}//${currentUrl.hostname}:3000/api`,
            serverPort: 3000,
            serverHost: currentUrl.hostname,
            environment: 'development'
        };
    }

    /**
     * 获取API基础URL
     * @returns {Promise<string>} API URL
     */
    async getApiUrl() {
        const config = await this.getConfig();
        return config.apiUrl;
    }

    /**
     * 获取服务器端口
     * @returns {Promise<number>} 服务器端口
     */
    async getServerPort() {
        const config = await this.getConfig();
        return config.serverPort;
    }

    /**
     * 获取服务器主机
     * @returns {Promise<string>} 服务器主机
     */
    async getServerHost() {
        const config = await this.getConfig();
        return config.serverHost;
    }

    /**
     * 重新加载配置
     * @returns {Promise<Object>} 新的配置对象
     */
    async reload() {
        this.config = null;
        this.isLoaded = false;
        this.loadPromise = null;
        return this.getConfig();
    }
}

// 创建全局配置管理器实例
const configManager = new ConfigManager();

// 导出配置管理器
export default configManager;

// 导出便捷方法
export const getConfig = () => configManager.getConfig();
export const getApiUrl = () => configManager.getApiUrl();
export const getServerPort = () => configManager.getServerPort();
export const getServerHost = () => configManager.getServerHost();
