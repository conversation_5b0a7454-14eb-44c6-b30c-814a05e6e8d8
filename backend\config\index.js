/**
 * 系统配置文件
 * 集中管理所有配置项
 */

// 加载环境变量
require('dotenv').config();

const path = require('path');

// 基础路径配置
const BASE_DIR = path.join(__dirname, '..');
const DATA_DIR = path.join(BASE_DIR, 'data');
const UPLOADS_DIR = path.join(BASE_DIR, 'uploads');
const LOGS_DIR = path.join(DATA_DIR, 'logs');

module.exports = {
    // 服务器配置
    server: {
        port: process.env.PORT || 5050, // 修改为非特权端口
        host: process.env.HOST || 'localhost'
    },

    // 安全配置
    security: {
        // IP白名单配置
        ipWhitelist: process.env.IP_WHITELIST ?
            process.env.IP_WHITELIST.split(',').map(ip => ip.trim()).filter(ip => ip) :
            [] // 空数组表示不启用IP限制
    },

    // JWT配置
    jwt: {
        secret: process.env.JWT_SECRET || 'makrite_application_system_secret_key',
        expiresIn: '24h'
    },

    // 文件路径配置
    paths: {
        base: BASE_DIR,
        data: DATA_DIR,
        uploads: UPLOADS_DIR,
        users: path.join(DATA_DIR, 'users'),
        applications: path.join(DATA_DIR, 'applications'),
        indexes: path.join(DATA_DIR, 'indexes'),
        logs: LOGS_DIR
    },

    // 文件配置
    files: {
        applications: path.join(DATA_DIR, 'applications.json'),
        users: path.join(DATA_DIR, 'users.json')
    },

    // 上传配置
    upload: {
        maxFileSize: 10 * 1024 * 1024, // 10MB，增加文件大小限制以支持质量报告文档
        allowedTypes: /pdf|doc|docx|xls|xlsx|jpg|jpeg|png|csv|text\/csv/
    },

    // 邮件配置
    email: {
        // Gmail服务配置
        service: 'gmail',
        auth: {
            user: process.env.EMAIL_USER || '', // Gmail邮箱地址
            pass: process.env.EMAIL_PASS || ''  // Gmail应用专用密码
        },
        // 发送配置
        from: process.env.EMAIL_FROM || process.env.EMAIL_USER || '',
        // 重试配置
        retry: {
            delay: 3000, // 基础重试间隔(毫秒)，会递增到最大30秒
            // 注意：邮件发送会一直重试直到成功
        },
        // 邮件模板配置
        templates: {
            subject: {
                submitted: '新申请提交通知 - 申请编号: {applicationNumber}',
                approved: '申请审批通过通知 - 申请编号: {applicationNumber}',
                rejected: '申请审批拒绝通知 - 申请编号: {applicationNumber}',
                completed: '申请审批完成通知 - 申请编号: {applicationNumber}',
                highAmount: '高金额申请通知 - 申请编号: {applicationNumber}',
                qualityReport: '检测报告上传通知 - 报告编号: {reportNumber}'
            }
        }
    },

    // 默认用户配置
    defaultUsers: [
        {
            username: 'admin',
            password: 'admin123',
            name: '管理员',
            role: 'admin',
            department: '管理部',
            email: '<EMAIL>'
        },
        {
            username: 'user',
            password: 'user123',
            name: '普通用户',
            role: 'user',
            department: '业务部',
            email: '<EMAIL>'
        }
    ],

    // 日志配置
    logging: {
        // 日志级别
        level: process.env.LOG_LEVEL || 'info',
        // 是否在控制台显示日志
        console: true,
        // 日志文件配置
        file: {
            // 是否启用文件日志
            enabled: true,
            // 日志文件最大大小
            maxSize: '20m',
            // 保留日志文件的天数
            maxDays: 14,
            // 日志文件名格式
            datePattern: 'YYYY-MM-DD',
            // 是否启用压缩
            zippedArchive: true,
            // 是否按级别分开存储
            separateByLevel: true
        },
        // 详细启动日志控制（设置环境变量 VERBOSE_LOGS=true 显示所有启动日志）
        verboseStartup: process.env.VERBOSE_LOGS === 'true',
        // 采样配置
        sampling: {
            // 是否启用采样
            enabled: true,
            // 静态资源请求采样率
            staticRate: 0.01, // 1%
            // 健康检查请求采样率
            healthRate: 0.001, // 0.1%
            // 普通API请求采样率
            apiRate: 0.1, // 10%
            // 调试日志采样率
            debugRate: 0.05 // 5%
        },
        // 性能监控
        performance: {
            // 是否启用性能监控
            enabled: true,
            // 慢请求阈值(毫秒)
            slowThreshold: 500,
            // 非常慢请求阈值(毫秒)
            verySlowThreshold: 1000
        },
        // 敏感字段（不会记录到日志中）
        sensitiveFields: [
            'password', 'token', 'secret', 'authorization',
            'apiKey', 'api_key', 'key', 'credential', 'pwd',
            'passphrase', 'pin', 'code', 'otp'
        ]
    }
};
