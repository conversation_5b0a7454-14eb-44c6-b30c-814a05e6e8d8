/**
 * PostgreSQL数据库管理器
 * 替代SQLite的数据库连接和管理功能
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

class PostgreSQLManager {
    constructor() {
        this.pool = null;
        this.config = this.loadConfig();
        this.migrationCompleted = false;
        this.init();
    }

    /**
     * 加载数据库配置
     */
    loadConfig() {
        try {
            // 优先使用环境变量
            if (process.env.DB_HOST) {
                return {
                    host: process.env.DB_HOST,
                    port: parseInt(process.env.DB_PORT) || 5432,
                    database: process.env.DB_NAME,
                    user: process.env.DB_USER,
                    password: process.env.DB_PASSWORD,
                    max: parseInt(process.env.DB_POOL_MAX) || 20,
                    min: parseInt(process.env.DB_POOL_MIN) || 5,
                    acquireTimeoutMillis: parseInt(process.env.DB_POOL_ACQUIRE_TIMEOUT) || 30000,
                    idleTimeoutMillis: parseInt(process.env.DB_POOL_IDLE_TIMEOUT) || 10000,
                    ssl: process.env.DB_SSL === 'true' ? {
                        rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false'
                    } : false
                };
            }

            // 使用配置文件
            const configPath = path.join(__dirname, '../config/database.json');
            if (fs.existsSync(configPath)) {
                const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
                const env = process.env.NODE_ENV || 'development';
                return config[env];
            }

            // 默认配置
            return {
                host: 'localhost',
                port: 5432,
                database: 'makrite_managementsystem',
                user: 'makrite_user',
                password: 'makrite_2024_secure',
                max: 20,
                min: 5,
                acquireTimeoutMillis: 30000,
                idleTimeoutMillis: 10000,
                ssl: false
            };
        } catch (error) {
            logger.error('加载数据库配置失败:', error);
            throw error;
        }
    }

    /**
     * 初始化数据库连接
     */
    async init() {
        try {
            // 创建连接池
            this.pool = new Pool(this.config);

            // 监听连接池事件
            this.pool.on('connect', (client) => {
                if (process.env.VERBOSE_LOGS === 'true') {
                    logger.debug('新的PostgreSQL连接已建立');
                }
            });

            this.pool.on('error', (err) => {
                logger.error('PostgreSQL连接池错误:', err);
            });

            // 测试连接
            await this.testConnection();

            // 只在详细日志模式下显示数据库初始化信息
            if (process.env.VERBOSE_LOGS === 'true') {
                logger.info(`PostgreSQL数据库连接池初始化成功`);
                logger.info(`数据库: ${this.config.database}@${this.config.host}:${this.config.port}`);
            }

        } catch (error) {
            logger.error('PostgreSQL数据库初始化失败:', error);
            throw error;
        }
    }

    /**
     * 测试数据库连接
     */
    async testConnection() {
        const client = await this.pool.connect();
        try {
            const result = await client.query('SELECT NOW() as current_time');
            logger.info('PostgreSQL数据库连接测试成功');
            return result.rows[0];
        } catch (error) {
            logger.error('PostgreSQL数据库连接测试失败:', error);
            throw error;
        } finally {
            client.release();
        }
    }

    /**
     * 执行查询
     */
    async query(text, params = []) {
        const client = await this.pool.connect();
        try {
            const start = Date.now();
            const result = await client.query(text, params);
            const duration = Date.now() - start;
            
            if (process.env.VERBOSE_LOGS === 'true' && duration > 100) {
                logger.debug('慢查询检测', {
                    query: text.substring(0, 100),
                    duration: `${duration}ms`,
                    rows: result.rowCount
                });
            }
            
            return result;
        } catch (error) {
            logger.error('PostgreSQL查询执行失败:', {
                query: text.substring(0, 100),
                error: error.message
            });
            throw error;
        } finally {
            client.release();
        }
    }

    /**
     * 执行事务
     */
    async transaction(callback) {
        const client = await this.pool.connect();
        try {
            await client.query('BEGIN');
            const result = await callback(client);
            await client.query('COMMIT');
            return result;
        } catch (error) {
            await client.query('ROLLBACK');
            logger.error('PostgreSQL事务执行失败:', error);
            throw error;
        } finally {
            client.release();
        }
    }

    /**
     * 批量插入
     */
    async batchInsert(tableName, columns, data) {
        if (!data || data.length === 0) {
            return { rowCount: 0 };
        }

        const client = await this.pool.connect();
        try {
            const columnNames = columns.join(', ');
            const placeholders = data.map((_, index) => {
                const rowPlaceholders = columns.map((_, colIndex) => 
                    `$${index * columns.length + colIndex + 1}`
                ).join(', ');
                return `(${rowPlaceholders})`;
            }).join(', ');

            const query = `INSERT INTO ${tableName} (${columnNames}) VALUES ${placeholders}`;
            const values = data.flat();

            const result = await client.query(query, values);
            return result;
        } catch (error) {
            logger.error('批量插入失败:', error);
            throw error;
        } finally {
            client.release();
        }
    }

    /**
     * 获取连接池统计信息
     */
    getStats() {
        if (!this.pool) {
            return { status: 'not_initialized' };
        }

        return {
            totalCount: this.pool.totalCount,
            idleCount: this.pool.idleCount,
            waitingCount: this.pool.waitingCount,
            maxConnections: this.config.max,
            minConnections: this.config.min
        };
    }

    /**
     * 数据库健康检查
     */
    async healthCheck() {
        try {
            const result = await this.query('SELECT version(), current_database(), current_user');
            const stats = this.getStats();
            
            return {
                status: 'healthy',
                connection: true,
                database: result.rows[0],
                pool: stats,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                connection: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 执行数据库备份
     */
    async backup(backupPath) {
        try {
            const { execSync } = require('child_process');
            const backupFile = path.join(backupPath, `${this.config.database}_${Date.now()}.sql`);
            
            // 确保备份目录存在
            if (!fs.existsSync(backupPath)) {
                fs.mkdirSync(backupPath, { recursive: true });
            }

            // 执行pg_dump
            const command = `pg_dump -h ${this.config.host} -p ${this.config.port} -U ${this.config.user} -d ${this.config.database} -f ${backupFile}`;
            execSync(command, { 
                env: { ...process.env, PGPASSWORD: this.config.password },
                stdio: 'inherit' 
            });

            logger.info(`PostgreSQL数据库备份成功: ${backupFile}`);
            return backupFile;
        } catch (error) {
            logger.error('PostgreSQL数据库备份失败:', error);
            throw error;
        }
    }

    /**
     * 关闭连接池
     */
    async close() {
        if (this.pool) {
            await this.pool.end();
            this.pool = null;
            logger.info('PostgreSQL连接池已关闭');
        }
    }

    /**
     * 获取数据库大小信息
     */
    async getDatabaseSize() {
        try {
            const result = await this.query(`
                SELECT 
                    pg_size_pretty(pg_database_size(current_database())) as size,
                    pg_database_size(current_database()) as bytes
            `);
            return result.rows[0];
        } catch (error) {
            logger.error('获取数据库大小失败:', error);
            return { size: 'unknown', bytes: 0 };
        }
    }

    /**
     * 获取表统计信息
     */
    async getTableStats() {
        try {
            const result = await this.query(`
                SELECT 
                    schemaname,
                    tablename,
                    n_tup_ins as inserts,
                    n_tup_upd as updates,
                    n_tup_del as deletes,
                    n_live_tup as live_tuples,
                    n_dead_tup as dead_tuples,
                    last_vacuum,
                    last_autovacuum,
                    last_analyze,
                    last_autoanalyze
                FROM pg_stat_user_tables
                ORDER BY n_live_tup DESC
            `);
            return result.rows;
        } catch (error) {
            logger.error('获取表统计信息失败:', error);
            return [];
        }
    }
}

module.exports = PostgreSQLManager;
