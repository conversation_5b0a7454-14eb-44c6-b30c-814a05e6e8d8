/**
 * PostgreSQL安装和配置脚本
 * 用于安装PostgreSQL依赖并创建数据库
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

class PostgreSQLInstaller {
    constructor() {
        this.dbName = 'makrite_managementsystem';
        this.dbUser = 'makrite_user';
        this.dbPassword = 'makrite_2024_secure';
        this.dbHost = 'localhost';
        this.dbPort = 5432;
    }

    /**
     * 安装PostgreSQL依赖
     */
    async installDependencies() {
        logger.info('开始安装PostgreSQL依赖...');
        
        try {
            // 安装pg依赖
            logger.info('安装pg模块...');
            execSync('npm install pg', { 
                cwd: path.join(__dirname, '..'),
                stdio: 'inherit' 
            });

            // 安装pg-pool用于连接池
            logger.info('安装pg-pool模块...');
            execSync('npm install pg-pool', { 
                cwd: path.join(__dirname, '..'),
                stdio: 'inherit' 
            });

            logger.info('PostgreSQL依赖安装完成');
            return true;
        } catch (error) {
            logger.error('安装PostgreSQL依赖失败:', error);
            return false;
        }
    }

    /**
     * 创建数据库配置文件
     */
    createDatabaseConfig() {
        logger.info('创建数据库配置文件...');
        
        const config = {
            development: {
                host: this.dbHost,
                port: this.dbPort,
                database: this.dbName,
                username: this.dbUser,
                password: this.dbPassword,
                dialect: 'postgres',
                logging: false,
                pool: {
                    max: 20,
                    min: 5,
                    acquire: 30000,
                    idle: 10000
                }
            },
            production: {
                host: process.env.DB_HOST || this.dbHost,
                port: process.env.DB_PORT || this.dbPort,
                database: process.env.DB_NAME || this.dbName,
                username: process.env.DB_USER || this.dbUser,
                password: process.env.DB_PASSWORD || this.dbPassword,
                dialect: 'postgres',
                logging: false,
                pool: {
                    max: 30,
                    min: 10,
                    acquire: 30000,
                    idle: 10000
                }
            }
        };

        const configPath = path.join(__dirname, '../config/database.json');
        
        // 确保config目录存在
        const configDir = path.dirname(configPath);
        if (!fs.existsSync(configDir)) {
            fs.mkdirSync(configDir, { recursive: true });
        }

        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
        logger.info(`数据库配置文件已创建: ${configPath}`);
    }

    /**
     * 创建环境变量模板
     */
    createEnvTemplate() {
        logger.info('创建环境变量模板...');
        
        const envTemplate = `# PostgreSQL数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=${this.dbName}
DB_USER=${this.dbUser}
DB_PASSWORD=${this.dbPassword}

# 连接池配置
DB_POOL_MAX=20
DB_POOL_MIN=5
DB_POOL_ACQUIRE_TIMEOUT=30000
DB_POOL_IDLE_TIMEOUT=10000

# 数据库SSL配置（生产环境建议启用）
DB_SSL=false
DB_SSL_REJECT_UNAUTHORIZED=false
`;

        const envPath = path.join(__dirname, '../../.env.postgresql');
        fs.writeFileSync(envPath, envTemplate);
        logger.info(`环境变量模板已创建: ${envPath}`);
        logger.info('请根据实际情况修改配置，并将其重命名为.env');
    }

    /**
     * 生成数据库创建SQL脚本
     */
    generateDatabaseCreationSQL() {
        logger.info('生成数据库创建SQL脚本...');
        
        const sql = `-- PostgreSQL数据库和用户创建脚本
-- 请以PostgreSQL超级用户身份执行此脚本

-- 创建用户
CREATE USER ${this.dbUser} WITH PASSWORD '${this.dbPassword}';

-- 创建数据库
CREATE DATABASE ${this.dbName} 
    WITH OWNER = ${this.dbUser}
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TEMPLATE = template0;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE ${this.dbName} TO ${this.dbUser};

-- 连接到新数据库并授予schema权限
\\c ${this.dbName}
GRANT ALL ON SCHEMA public TO ${this.dbUser};
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ${this.dbUser};
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ${this.dbUser};

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO ${this.dbUser};
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO ${this.dbUser};

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

\\echo '数据库创建完成！'
\\echo '数据库名: ${this.dbName}'
\\echo '用户名: ${this.dbUser}'
\\echo '请妥善保管数据库密码'
`;

        const sqlPath = path.join(__dirname, '../database/create_postgresql_db.sql');
        fs.writeFileSync(sqlPath, sql);
        logger.info(`数据库创建SQL脚本已生成: ${sqlPath}`);
        
        return sqlPath;
    }

    /**
     * 显示安装说明
     */
    showInstallationInstructions() {
        console.log('\n' + '='.repeat(80));
        console.log('PostgreSQL安装和配置说明');
        console.log('='.repeat(80));
        console.log('\n1. 安装PostgreSQL服务器:');
        console.log('   Windows: 下载并安装 https://www.postgresql.org/download/windows/');
        console.log('   macOS: brew install postgresql');
        console.log('   Ubuntu: sudo apt-get install postgresql postgresql-contrib');
        console.log('\n2. 启动PostgreSQL服务:');
        console.log('   Windows: 通过服务管理器启动');
        console.log('   macOS/Linux: sudo systemctl start postgresql');
        console.log('\n3. 执行数据库创建脚本:');
        console.log(`   psql -U postgres -f backend/database/create_postgresql_db.sql`);
        console.log('\n4. 配置环境变量:');
        console.log('   将 .env.postgresql 重命名为 .env 并根据实际情况修改');
        console.log('\n5. 运行迁移脚本:');
        console.log('   node backend/scripts/migrateToPostgreSQL.js');
        console.log('\n' + '='.repeat(80));
    }

    /**
     * 执行完整安装流程
     */
    async install() {
        try {
            logger.info('开始PostgreSQL安装和配置流程...');
            
            // 1. 安装依赖
            const depsInstalled = await this.installDependencies();
            if (!depsInstalled) {
                throw new Error('依赖安装失败');
            }

            // 2. 创建配置文件
            this.createDatabaseConfig();
            
            // 3. 创建环境变量模板
            this.createEnvTemplate();
            
            // 4. 生成数据库创建脚本
            this.generateDatabaseCreationSQL();
            
            // 5. 显示安装说明
            this.showInstallationInstructions();
            
            logger.info('PostgreSQL安装配置完成！');
            return true;
            
        } catch (error) {
            logger.error('PostgreSQL安装配置失败:', error);
            return false;
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const installer = new PostgreSQLInstaller();
    installer.install().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = PostgreSQLInstaller;
